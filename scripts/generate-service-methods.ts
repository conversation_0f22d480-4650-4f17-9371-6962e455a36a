import fs from 'node:fs';
import path from 'node:path';

const PROTO_DIR = path.resolve(__dirname, '../proto');
const GENERATED_DIR = path.resolve(__dirname, '../src/generated');
const OUTPUT_PATH = path.resolve(GENERATED_DIR, 'grpc-types.ts');

function findAllServiceProtos(): string[] {
    const result: string[] = [];

    function walk(dir: string) {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                walk(fullPath);
            } else if (entry.name === 'service.proto') {
                result.push(fullPath);
            }
        }
    }

    walk(PROTO_DIR);
    return result;
}

function getProtoPackageAndService(
    content: string,
): { packageName: string; serviceName: string } | null {
    const packageMatch = content.match(/^[ \t]*package (.*?);/m);
    const serviceMatch = content.match(/^[ \t]*service (.*?)( |\{)/m);
    if (!packageMatch || !serviceMatch) return null;
    return {
        packageName: packageMatch[1],
        serviceName: serviceMatch[1],
    };
}

function capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function getMethods(dir: string): string[] {
    const methodsPath = path.resolve(dir, 'methods');
    if (!fs.existsSync(methodsPath)) return [];
    return fs.readdirSync(methodsPath).filter((name) => {
        const fullPath = path.join(methodsPath, name);
        return fs.statSync(fullPath).isDirectory();
    });
}

function getVersions(messagesTsPath: string): string[] {
    if (!fs.existsSync(messagesTsPath)) return [];
    const file = fs.readFileSync(messagesTsPath, 'utf-8');
    const matches = [...file.matchAll(/\b(v[0-9]+)\?:/g)];
    return matches.map((m) => m[1]);
}

const serviceMethods: Record<string, any> = {};
const imports: Set<string> = new Set();
const serviceDirMap: Record<string, string> = {};

for (const protoPath of findAllServiceProtos()) {
    const protoContent = fs.readFileSync(protoPath, 'utf-8');
    const parsed = getProtoPackageAndService(protoContent);
    if (!parsed) continue;

    const { packageName, serviceName } = parsed;
    const serviceKey = serviceName;

    const protoRelativeDir = path.relative(PROTO_DIR, path.dirname(protoPath));
    const generatedBasePath = path.resolve(GENERATED_DIR, protoRelativeDir);
    const methods = getMethods(path.resolve(PROTO_DIR, protoRelativeDir));

    const serviceEntry: Record<string, any> = {};

    for (const method of methods) {
        const methodName = method
            .split('-')
            .map((p) => capitalize(p))
            .join('');

        const importIdentifierReq = `${capitalize(protoRelativeDir)}${methodName}Request`;
        const importIdentifierRes = `${capitalize(protoRelativeDir)}${methodName}Response`;

        imports.add(
            `type { ${importIdentifierReq}, ${importIdentifierRes} } from './${protoRelativeDir}/methods/${method}/messages';`,
        );

        const messagesTsPath = path.resolve(
            generatedBasePath,
            'methods',
            method,
            'messages.ts',
        );
        const versions = getVersions(messagesTsPath);

        const versionMap: Record<
            string,
            { request: string; response: string }
        > = {};

        for (const version of versions) {
            versionMap[version] = {
                request: `${importIdentifierReq}['${version}']`,
                response: `${importIdentifierRes}['${version}']`,
            };
        }

        serviceEntry[methodName] = versionMap;
    }

    serviceMethods[serviceKey] = serviceEntry;
    serviceDirMap[serviceKey] = protoRelativeDir.split(path.sep)[0];
}

const fileContent = `// GENERATED FILE — DO NOT EDIT

${[...imports].map((imp) => `import ${imp}`).join('\n')}

export type ServiceMethods = ${JSON.stringify(serviceMethods, null, 4)
    .replace(/"(\w+)":/g, '$1:')
    .replace(/"/g, '')};

export type ServiceName = keyof ServiceMethods;
export type ServiceMethod<T extends ServiceName> = keyof ServiceMethods[T];
export type ServiceVersion<T extends ServiceName, M extends ServiceMethod<T>> = keyof ServiceMethods[T][M];

type VersionEntry = {
    request: any;
    response: any;
};

export type ServiceRequest<
    T extends ServiceName,
    M extends ServiceMethod<T>,
    V extends ServiceVersion<T, M>
> = ServiceMethods[T][M][V] extends VersionEntry
    ? ServiceMethods[T][M][V]['request']
    : never;

export type ServiceResponse<
    T extends ServiceName,
    M extends ServiceMethod<T>,
    V extends ServiceVersion<T, M>
> = ServiceMethods[T][M][V] extends VersionEntry
    ? ServiceMethods[T][M][V]['response']
    : never;

export type ServiceDirType = ${[...new Set(Object.values(serviceDirMap))]
    .map((dir) => `'${dir}'`)
    .join(' | ')};

export const ServiceDirMap: Record<ServiceName, ServiceDirType> = {
${Object.entries(serviceDirMap)
    .map(([key, dir]) => `    ${key}: '${dir}',`)
    .join('\n')}
};
`;

fs.writeFileSync(OUTPUT_PATH, fileContent);
console.log(`✅ grpc-types.ts сгенерирован: ${OUTPUT_PATH}`);
