syntax = "proto3";

package monolith;

// Импортируем определения конкретных версий
import "monolith/methods/get-user-permission/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message MonolithGetUserPermissionRequest {
  oneof version {
    V1MonolithGetUserPermissionRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message MonolithGetUserPermissionResponse {
  oneof version {
    V1MonolithGetUserPermissionResponse v1 = 1;
  }
}
