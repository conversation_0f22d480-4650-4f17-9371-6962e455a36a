# @skillspace/grpc

🚀 Расширение для NestJS микросервисов, добавляющее поддержку **версионированных gRPC-методов** при сохранении **единого
сервиса** и полной типизации.

---

## 📆 Возможности

- ✅ Единый `service.proto` на сервис
- ✅ Версионированные сообщения через `oneof`
- ✅ Автогенерация типов и proto-путей
- ✅ Удобный декоратор `@GrpcSubscribe`
- ✅ Типизированный клиент с `.send()`
- ✅ Nest-модули: `GrpcModule` (Consumer) и `GrpcClientModule` (Producer)
- ✅ Гибкий gRPC-интерцептор и фильтр ошибок
- ✅ Полная логгируемость и `x-request-id`
- ✅ Поддержка NestJS `v9` и `v10`

## 📁 Структура proto

```
proto/
└── your-service/
    ├── service.proto
    └── methods/
        └── some-method/
            ├── messages.proto
            └── versions/
                ├── v1.proto
                └── v2.proto
```

### 🧠 Как это работает

- `service.proto` — содержит единый gRPC-сервис с методами
- `methods/*/messages.proto` — обертка с `oneof` для объединения версий
- `methods/*/versions/*` — конкретные версии структуры запроса и ответа

Пример:

```proto
message GetUserRequest {
  oneof version {
    GetUserRequestV1 v1 = 1;
    GetUserRequestV2 v2 = 2;
  }
}
```

Плюсы:

- 📌 Поддержка нескольких версий без дублирования сервисов
- 🧩 Явная backward-compatible эволюция
- 💡 Генерация строго типизированных `ServiceMethods`

---

## 🧠 Работа с proto

Наша gRPC-библиотека ориентирована на **версионирование сообщений**, а не сервисов. Это позволяет:

- Иметь **единый gRPC-сервис** (`MonolithService`, `NotificationsService`, ...)
- Обновлять структуру сообщений без ломания обратной совместимости
- Поддерживать одновременно несколько версий методов

### Пример структуры

```
proto/
└── monolith/
    ├── service.proto
    └── methods/
        └── get-user/
            ├── messages.proto
            └── versions/
                ├── v1.proto
                └── v2.proto
```

- `service.proto` содержит `rpc GetUser(GetUserRequest) returns (GetUserResponse)`
- `messages.proto` содержит oneof `v1`, `v2`, `v3`, ...
- `versions/*` содержат конкретные версии, которые можно развивать независимо

Типы автоматически генерируются в `src/generated/`

---

## 🚀 Быстрый старт

### 1. Установка

```json
// package.json
{
    "@skillspace/grpc": "git+https://gitlab+deploy-token-8:<EMAIL>/skillspace/microservices/contracts/grpc-contraracts.git#1.0.0"
}
```

---

### 2. Сервер (Consumer)

```ts

@Module({
    imports: [
        GrpcModule.register({
            packageName: 'monolith',
            address: '0.0.0.0:50051',
        }),
    ],
})
export class AppModule {
}
```

```typescript
@GrpcSubscribe('MonolithService', 'GetUserPermission', 'v1')
handleGetUserPermission(data
:
ServiceRequest<'MonolithService', 'GetUser', 'v1'>;
):
Promise < ServiceResponse < 'MonolithService', 'GetUser', 'v1' >> {
    return { allowed: true },;
}
;
```

---

### 3. Клиент (Producer)

```ts

@Module({
    imports: [
        GrpcClientModule.register({
            serviceName: 'MonolithService',
            address: '0.0.0.0:50051',
        }),
    ],
})
export class MyFeatureModule {
}
```

```ts

@Injectable()
export class MyService {
    constructor(
        @GrpcInjectClient('MonolithService') private readonly grpc: GrpcClient<'MonolithService'>,
    ) {
    }

    async doSomething() {
        const res = await this.grpc.send('GetUserPermission', 'v1', { userId: '123' });
    }
}
```

---

## 🧬 Автогенерация

```bash
pnpm run generate
```

Создаются:

- `generated/grpc-types.ts` — типы `ServiceMethods`, `ServiceName`, `ServiceRequest` и пр.
- `generated/proto-paths.ts` — список абсолютных путей к proto-файлам
- `ServiceDirMap` и `ServiceDirType` — мапа `service → protoDir`

---

## ⚙️ Команды

```bash
pnpm run generate         # Генерация типов и proto-путей
pnpm run build            # Сборка
pnpm run dev              # Вотчер на proto/ и generated/
pnpm run start-test-app   # Минималка Nest-приложения для отладки
```

---

## 💡 Использование локальной версии

Для использования локальной версии библиотеки вместо удалённой:

```json
// package.json
{
    "@skillspace/grpc": "file:../../contracts/grpc-contracts"
}
```

### ИЛИ

Добавить в package.json

```json
// package.json
{
    "pnpm": {
        "overrides": {
            "@skillspace/grpc": "file:../../contracts/grpc-contracts"
        }
    }
}
```

---

## 🔄 Чтобы сервис получил новые изменения локальной либы:

### 1. Выполнить в библиотеке **@skillspace/grpc**:

```shell
pnpm run build 
```

### 2. Пересобрать зависимости в **сервисе**

```shell
pnpm i
```

### 3. Пересобрать докер (если требуется)

---

## 🤝 Совместимость

| NestJS | Поддержка |
|--------|-----------|
| v10    | ✅         |
| v9     | ✅         |
| < v9   | ❌         |

---

## 💪 TODO

- [ ] `class-validator`
- [ ] gRPC Streams
