syntax = "proto3";

package editor;

// Импортируем определения конкретных версий
import "editor/methods/remove-many-pages/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message EditorRemoveManyPagesRequest {
  oneof version {
    V1EditorRemoveManyPagesRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message EditorRemoveManyPagesResponse {
  oneof version {
    V1EditorRemoveManyPagesResponse v1 = 1;
  }
}
