// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: plans/methods/is-feature-available/versions/v1.proto

/* eslint-disable */

export const protobufPackage = "plans";

export interface V1PlansIsFeatureAvailableRequest {
  featureId: string;
  customerId: string;
}

export interface V1PlansIsFeatureAvailableResponse {
  enabled: boolean;
  error: string;
  available: number;
  type: string;
}
