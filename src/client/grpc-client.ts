import { randomUUID } from 'node:crypto';
import path from 'node:path';

import * as grpc from '@grpc/grpc-js';
import { Metadata, ServiceError } from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import { Logger } from '@nestjs/common';

import {
    ServiceDirMap,
    ServiceMethod,
    ServiceName,
    ServiceRequest,
    ServiceResponse,
    ServiceVersion,
} from '../generated';

export class GrpcClient<S extends ServiceName> {
    private readonly clientCache = new Map<string, any>();
    private readonly logger: Logger;

    constructor(
        private readonly serviceName: S,
        private readonly target: string,
    ) {
        this.logger = new Logger(`GrpcClient<${String(this.serviceName)}>`, {
            timestamp: true,
        });
    }

    private loadClient(service: S): any {
        if (this.clientCache.has(service)) {
            return this.clientCache.get(service);
        }

        const dir = ServiceDirMap[service];
        if (!dir)
            throw new Error(`Не найдена директория для сервиса "${service}"`);

        const protoDir = path.join(__dirname, '..', '..', 'proto');
        const protoPath = path.join(protoDir, dir, 'service.proto');

        const packageDef = protoLoader.loadSync(protoPath, {
            keepCase: true,
            longs: String,
            enums: String,
            defaults: true,
            oneofs: true,
            includeDirs: [protoDir],
        });

        const grpcPackage = grpc.loadPackageDefinition(packageDef) as any;
        const grpcPkg = grpcPackage[dir];
        const ClientConstructor = grpcPkg?.[service];

        if (!ClientConstructor) {
            throw new Error(
                `gRPC клиент "${service}" не найден в пакете "${dir}"`,
            );
        }

        const client = new ClientConstructor(
            this.target,
            grpc.credentials.createInsecure(),
        );

        this.clientCache.set(service, client);
        return client;
    }

    async send<M extends ServiceMethod<S>, V extends ServiceVersion<S, M>>(
        method: M,
        version: V,
        data: ServiceRequest<S, M, V>,
    ): Promise<ServiceResponse<S, M, V>> {
        const requestId = randomUUID();
        const client = this.loadClient(this.serviceName);
        const wrappedRequest = { [version]: data };

        const metadata = new Metadata();
        metadata.set('x-request-id', requestId);

        this.logger.debug(
            data,
            `[${requestId}] → ${this.serviceName}.${<string>method} (${<string>version})`,
        );

        return new Promise((resolve, reject) => {
            client[method](
                wrappedRequest,
                metadata,
                (err: ServiceError | null, response: any) => {
                    if (err) {
                        this.logger.error(
                            `[${requestId}] ← ERROR: ${err.message}`,
                        );
                        return reject(err);
                    }

                    this.logger.debug(
                        response,
                        `[${requestId}] → ${'response'})`,
                    );

                    const versionedResponse = response?.[version];
                    if (!versionedResponse) {
                        const msg = `Ответ не содержит поля версии "${<string>version}"`;
                        this.logger.warn(`[${requestId}] ← ${msg}`);
                        return reject(new Error(msg));
                    }

                    this.logger.debug(
                        versionedResponse,
                        `[${requestId}] ← ${this.serviceName}.${<string>method} (${<string>version})`,
                    );

                    resolve(versionedResponse);
                },
            );
        });
    }
}
