// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/get-kinescope-by-school/messages.proto

/* eslint-disable */
import { type V1MonolithGetKinescopeBySchoolRequest, type V1MonolithGetKinescopeBySchoolResponse } from "./versions/v1";

export const protobufPackage = "monolith";

/** Универсальное описание запроса с выбором версии */
export interface MonolithGetKinescopeBySchoolRequest {
  v1?: V1MonolithGetKinescopeBySchoolRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface MonolithGetKinescopeBySchoolResponse {
  v1?: V1MonolithGetKinescopeBySchoolResponse | undefined;
}
