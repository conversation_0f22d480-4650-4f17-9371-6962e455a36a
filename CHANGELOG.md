## 1.7.0 (2025-06-09)

* feat: Контракты создания документов в редакторе ([ada537ebf1aa1d3a8a86fe127a264ad98b155485](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/ada537ebf1aa1d3a8a86fe127a264ad98b155485))

## 1.6.0 (2025-06-04)

* feat: Добавил grpc контракт на валидацию unionAuthKey пользователя ([0b859f06dc43204a1f42180a173189a26c07be11](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/0b859f06dc43204a1f42180a173189a26c07be11))

## 1.5.0 (2025-05-29)

* feat: добавлена отладочная логгеровка ответа gRPC клиента ([95b326958331e437716415e896a9c1fcd8179bb7](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/95b326958331e437716415e896a9c1fcd8179bb7))

## 1.4.0 (2025-05-28)

* feat: обновлен grpc-клиент и сервер для использования address вместо port ([32c1ad9425189dfe075bbfc991f0e84d7392bc97](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/32c1ad9425189dfe075bbfc991f0e84d7392bc97))

## <small>1.3.1 (2025-05-21)</small>

* fix: Добавил директории для методов монолита ([6e03bb1a88889682e18ab6f552c6e8b0e5608c18](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/6e03bb1a88889682e18ab6f552c6e8b0e5608c18))
* fix: Сборка ([0061f63947f11db101c4515c3b8718b2026a406b](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/0061f63947f11db101c4515c3b8718b2026a406b))

## 1.3.0 (2025-05-20)

* feat: Контракт для проверки лимитов на создание встроенных вебинаров ([6d6506bf0ab658d18c183886cffc2de1d2df0420](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/6d6506bf0ab658d18c183886cffc2de1d2df0420))

## <small>1.2.2 (2025-05-20)</small>

* fix: build ([fcd6fe6cdc222af1b2cc2c29d120a288dc9b311c](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/fcd6fe6cdc222af1b2cc2c29d120a288dc9b311c))

## <small>1.2.1 (2025-05-20)</small>

* fix: гит игнор ([c984ad20811682a6e86b3982d14d67304794ccf4](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/c984ad20811682a6e86b3982d14d67304794ccf4))

## 1.2.0 (2025-05-19)

* refactor: заменил .releaserc.json на release.config.cjs ([d0432e57e43ed85f6e30b628140d4d711608a5a6](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/d0432e57e43ed85f6e30b628140d4d711608a5a6))
* fix: поменял CI, исправил название планов ([ed64f71f71cbd8dda4a8fb4368454e55c1413c51](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/ed64f71f71cbd8dda4a8fb4368454e55c1413c51))
* fix: поменял CI, исправил название планов ([21b8d2144b5fd34e9c28cd3b225a33e1129cb600](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/21b8d2144b5fd34e9c28cd3b225a33e1129cb600))
* feat: Генерация типов ([8fa215228ce427daa103d07ec3c7b423e5d63cc8](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/8fa215228ce427daa103d07ec3c7b423e5d63cc8))
* feat: Контракт проверки доступности фич ([9607faea73513b49688cd4ce5ebcf4f249c8ddab](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/9607faea73513b49688cd4ce5ebcf4f249c8ddab))
* Добавлен временный job для обработки merge-запросов в конфигурацию CI. ([c7aa4661008b856c3bc708d14e8cafd1a84aaa9b](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/c7aa4661008b856c3bc708d14e8cafd1a84aaa9b))
* Добавлен временный job для обработки merge-запросов в конфигурацию CI. ([6a15a8c9e11c7cd58fa5eebb41d9b153171b2a9e](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/6a15a8c9e11c7cd58fa5eebb41d9b153171b2a9e))
* Добавлен временный job для обработки merge-запросов и обновлены репозитории Alpine в CI конфигурации. ([cba0784b60e654a5daaf5c189f48df759a4bc975](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/cba0784b60e654a5daaf5c189f48df759a4bc975))
* Обновлена конфигурация CI: удален временный job для merge-запросов и добавлено ограничение на выполнение сборки только для ветки main. ([8e3faf38892678cfd3319ae02cffab452d9a8c29](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/8e3faf38892678cfd3319ae02cffab452d9a8c29))
* Обновлена конфигурация CI: удален временный job для merge-запросов и добавлено ограничение на выполнение сборки только для ветки main. ([3cf7ae690059f7f0881f6c1377f576537614a217](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/3cf7ae690059f7f0881f6c1377f576537614a217))
* Удален временный job для обработки merge-запросов в конфигурации CI. ([b8807e443a7474ecfd65e8008a8235019c78fdf2](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/b8807e443a7474ecfd65e8008a8235019c78fdf2))
* style: линт ([cb3085dfaaa3bd7a8371f71df6b7d4293c43ba64](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/cb3085dfaaa3bd7a8371f71df6b7d4293c43ba64))

## 1.1.0 (2025-04-16)

* Merge branch 'v1.1.0' into 'main' ([803c4a7](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/803c4a7))
* Merge branch 'v1.1.0' into 'main' ([15e0261](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/15e0261))
* Merge branch 'v1.1.0' into 'main' ([9bf7de6](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/9bf7de6))
* feat: проверка ci ([484e5bb](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/484e5bb))
* ci: publishConfig.registry ([1bf3133](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/1bf3133))
* ci: тест ([6e0fe90](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/6e0fe90))

## 1.0.0 (2025-04-15)

* grpc ([22ad0aa](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/22ad0aa))
* grpc v1 ([5ff5f6a](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/5ff5f6a))
* grpc v1 ([4b1633b](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/4b1633b))
* grpc v1 ([17ba334](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/17ba334))
* grpc v1 ([7e77b3b](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/7e77b3b))
* grpc v1 ([d586683](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/d586683))
* grpc v1.0.1 ([d1fb45e](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/d1fb45e))
* Initial commit ([fff9a83](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/fff9a83))
* Merge branch 'v1.0.0' into 'main' ([f3b1206](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/f3b1206))
* Merge branch 'v1.0.0' into 'main' ([c5ce406](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/c5ce406))
* Merge branch 'v1.0.0' into 'main' ([c521035](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/c521035))
* Merge branch 'v1.0.0' into 'main' ([f62e86b](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/f62e86b))
* Merge branch 'v1.0.0' into 'main' ([fdef6b9](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/fdef6b9))
* Merge branch 'v1.0.0' into 'main' ([ac0db3c](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/ac0db3c))
* Merge branch 'v1.0.3' into 'main' ([c4b3240](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/c4b3240))
* Merge branch 'v1.1.0' into 'main' ([4719181](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/4719181))
* v1.0.3 ([470cbb4](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/470cbb4))
* v1.0.3 ([d6b7c69](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/d6b7c69))
* ci: добавил protobuf в установки ([2012f0a](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/2012f0a))
* ci: проверка билда перед мркой ([c4400a8](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/c4400a8))
* feat: новый контракт ([3466989](https://gitlab.sksp.site/skillspace/microservices/contracts/grpc-contracts/commit/3466989))

## v1.0.3

###### 03.04.2025

1. Правка контракта

## v1.0.2

###### 31.03.2025

1. Правка контракта

## v1.0.1

###### 28.03.2025

1. Правка контракта

## v1.0.0

###### 28.03.2025

1. Старт либы
