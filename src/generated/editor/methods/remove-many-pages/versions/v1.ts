// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: editor/methods/remove-many-pages/versions/v1.proto

/* eslint-disable */

export const protobufPackage = "editor";

export interface V1EditorRemoveManyPagesRequest {
  /** список id страниц для удаления */
  pageIds: string[];
}

export interface V1EditorRemoveManyPagesResponse {
  /** флаг успешного удаления */
  success: boolean;
  /** список id удаленных страниц */
  removedPageIds: string[];
}
