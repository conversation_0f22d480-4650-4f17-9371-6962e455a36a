// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/service.proto

/* eslint-disable */
import {
  type MonolithCheckLimitForInternalWebinarFeatureRequest,
  type MonolithCheckLimitForInternalWebinarFeatureResponse,
} from "./methods/check-limit-for-internal-webinar-feature/messages";
import {
  type MonolithCheckValidityUnionAuthKeyRequest,
  type MonolithCheckValidityUnionAuthKeyResponse,
} from "./methods/check-validity-union-auth-key/messages";
import {
  type MonolithGetKinescopeBySchoolRequest,
  type MonolithGetKinescopeBySchoolResponse,
} from "./methods/get-kinescope-by-school/messages";
import {
  type MonolithGetUserPermissionRequest,
  type MonolithGetUserPermissionResponse,
} from "./methods/get-user-permission/messages";
import {
  type MonolithIsEnableWebinarFeatureRequest,
  type MonolithIsEnableWebinarFeatureResponse,
} from "./methods/is-enable-webinar-feature/messages";

export const protobufPackage = "monolith";

export interface MonolithService {
  /** Получение информации об идентификаторе проекта школы в kinescope по uuid школы */
  GetKinescopeBySchool(request: MonolithGetKinescopeBySchoolRequest): Promise<MonolithGetKinescopeBySchoolResponse>;
  /** Получение информации о доступности фичи встроенных вебинаров для школы */
  IsEnableWebinarFeature(
    request: MonolithIsEnableWebinarFeatureRequest,
  ): Promise<MonolithIsEnableWebinarFeatureResponse>;
  /** Проверка количества доступных встроенных вебинаров для школы в месяц */
  CheckLimitForInternalWebinarFeature(
    request: MonolithCheckLimitForInternalWebinarFeatureRequest,
  ): Promise<MonolithCheckLimitForInternalWebinarFeatureResponse>;
  /** Получение информации о доступах пользователя в школе */
  GetUserPermission(request: MonolithGetUserPermissionRequest): Promise<MonolithGetUserPermissionResponse>;
  /** Валидация union auth key пользователя и подключения ТГ */
  CheckValidityUnionAuthKey(
    request: MonolithCheckValidityUnionAuthKeyRequest,
  ): Promise<MonolithCheckValidityUnionAuthKeyResponse>;
}
