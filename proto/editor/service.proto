syntax = "proto3";

package editor;

import "editor/methods/create-many-pages/messages.proto";
import "editor/methods/remove-many-pages/messages.proto";

service EditorService {
  // Создание страниц
  rpc CreateManyPages(EditorCreateManyPagesRequest) returns (EditorCreateManyPagesResponse) {};

  // Удаление страниц
  rpc RemoveManyPages(EditorRemoveManyPagesRequest) returns (EditorRemoveManyPagesResponse) {};
}
