syntax = "proto3";

package plans;

// Импортируем определения конкретных версий
import "plans/methods/is-feature-available/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message PlansIsFeatureAvailableRequest {
  oneof version {
    V1PlansIsFeatureAvailableRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message PlansIsFeatureAvailableResponse {
  oneof version {
    V1PlansIsFeatureAvailableResponse v1 = 1;
  }
}
