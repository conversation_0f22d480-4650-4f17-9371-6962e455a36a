module.exports = {
    root: true,
    parser: '@typescript-eslint/parser',
    parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
        sourceType: 'module',
    },
    plugins: [
        '@typescript-eslint',
        'import',
        'simple-import-sort',
        'unused-imports',
    ],
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:prettier/recommended',
        'plugin:import/recommended',
    ],
    ignorePatterns: ['dist/', 'node_modules/'],
    settings: {
        'import/resolver': {
            typescript: {
                alwaysTryTypes: true,
                project: './tsconfig.json',
            },
        },
    },
    rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/ban-ts-comment': 'off',
        '@typescript-eslint/explicit-module-boundary-types': 'off',

        '@typescript-eslint/no-unused-vars': [
            'warn',
            {
                argsIgnorePattern: '^_', // ⬅️ игнорировать аргументы, начинающиеся с "_"
                varsIgnorePattern: '^_', // (на всякий случай, если переменные)
            },
        ],

        'simple-import-sort/imports': 'error',
        'simple-import-sort/exports': 'error',

        'unused-imports/no-unused-imports': 'error',

        'import/first': 'error',
        'import/newline-after-import': 'error',
        'import/no-duplicates': 'error',
    },
};
