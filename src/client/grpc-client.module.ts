import { DynamicMod<PERSON>, Logger, Module, Provider } from '@nestjs/common';

import { ServiceName } from '../generated';
import { GrpcClient } from './grpc-client';

export interface GrpcClientModuleOptions {
    serviceName: ServiceName;
    address: string;
}

export interface GrpcClientModuleAsyncOptions {
    serviceName: ServiceName;
    useFactory: (...args: any[]) => Promise<string> | string;
    inject?: any[];
}

@Module({})
export class GrpcClientModule {
    static register(options: GrpcClientModuleOptions): DynamicModule {
        return this.createClientProvider(options.serviceName, options.address);
    }

    static registerAsync(options: GrpcClientModuleAsyncOptions): DynamicModule {
        const token = `GRPC_CLIENT__${options.serviceName}`;
        const logger = new Logger(`GrpcClient<${options.serviceName}>`);

        const provider: Provider = {
            provide: token,
            useFactory: async (...args: any[]) => {
                const address = await options.useFactory(...args);
                const client = new GrpcClient(options.serviceName, address);
                logger.log(
                    `🔌 Подключен к ${options.serviceName} на ${address}`,
                );
                return client;
            },
            inject: options.inject || [],
        };

        return {
            module: GrpcClientModule,
            providers: [provider],
            exports: [provider],
        };
    }

    private static createClientProvider(
        serviceName: ServiceName,
        address: string,
    ): DynamicModule {
        const logger = new Logger(`GrpcClient<${serviceName}>`);

        const provider: Provider = {
            provide: `GRPC_CLIENT__${serviceName}`,
            useFactory: () => {
                const client = new GrpcClient(serviceName, address);
                logger.log(`🔌 Подключен к ${serviceName} на ${address}`);
                return client;
            },
        };

        return {
            module: GrpcClientModule,
            providers: [provider],
            exports: [provider],
        };
    }
}
