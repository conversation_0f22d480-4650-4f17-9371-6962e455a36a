// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/is-enable-webinar-feature/messages.proto

/* eslint-disable */
import {
  type V1MonolithIsEnableWebinarFeatureRequest,
  type V1MonolithIsEnableWebinarFeatureResponse,
} from "./versions/v1";

export const protobufPackage = "monolith";

/** Универсальное описание запроса с выбором версии */
export interface MonolithIsEnableWebinarFeatureRequest {
  v1?: V1MonolithIsEnableWebinarFeatureRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface MonolithIsEnableWebinarFeatureResponse {
  v1?: V1MonolithIsEnableWebinarFeatureResponse | undefined;
}
