import {
    ArgumentsHost,
    Catch,
    ExceptionFilter,
    Logger,
    ExecutionContext,
} from '@nestjs/common';
import { Metadata, status, ServiceError } from '@grpc/grpc-js';

@Catch()
export class GrpcExceptionFilter implements ExceptionFilter {
    private readonly logger = new Logger(GrpcExceptionFilter.name);

    catch(exception: any, host: ArgumentsHost) {
        const ctx = host.switchToRpc();
        const request = ctx.getData();

        const execCtx = host as ExecutionContext;
        const handler = execCtx.getHandler?.();
        const method = handler?.name ?? 'unknown';

        const message = exception?.message ?? 'Неизвестная ошибка';
        const code = exception?.code ?? status.INTERNAL;
        const stack = exception?.stack;

        const metadata = new Metadata();
        const requestId = ctx
            .getContext()
            ?.metadata?.get?.('x-request-id')?.[0];
        if (requestId) {
            metadata.set('x-request-id', requestId);
        }

        this.logger.error(
            {
                requestId,
                request,
                error: message,
                stack,
            },
            `❌ [gRPC] Ошибка в методе: ${method}`,
        );

        const error: Partial<ServiceError> = {
            name: 'GrpcError',
            message,
            code,
            metadata,
            details: exception?.details ?? '',
        };

        // Приводим явно к any — gRPC поймёт нужные поля
        throw error as ServiceError;
    }
}
