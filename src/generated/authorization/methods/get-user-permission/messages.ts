// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: authorization/methods/get-user-permission/messages.proto

/* eslint-disable */
import {
  type V1AuthorizationGetUserPermissionRequest,
  type V1AuthorizationGetUserPermissionResponse,
} from "./versions/v1";

export const protobufPackage = "authorization";

/** Универсальное описание запроса с выбором версии */
export interface AuthorizationGetUserPermissionRequest {
  v1?: V1AuthorizationGetUserPermissionRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface AuthorizationGetUserPermissionResponse {
  v1?: V1AuthorizationGetUserPermissionResponse | undefined;
}
