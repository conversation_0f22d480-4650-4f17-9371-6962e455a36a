// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: editor/methods/remove-many-pages/messages.proto

/* eslint-disable */
import { type V1EditorRemoveManyPagesRequest, type V1EditorRemoveManyPagesResponse } from "./versions/v1";

export const protobufPackage = "editor";

/** Универсальное описание запроса с выбором версии */
export interface EditorRemoveManyPagesRequest {
  v1?: V1EditorRemoveManyPagesRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface EditorRemoveManyPagesResponse {
  v1?: V1EditorRemoveManyPagesResponse | undefined;
}
