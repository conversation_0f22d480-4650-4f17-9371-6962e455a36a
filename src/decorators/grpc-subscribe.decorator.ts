import { SetMetadata } from '@nestjs/common';
import type {
    ServiceName,
    ServiceMethod,
    ServiceVersion,
} from '../generated/grpc-types';

export const GRPC_SUBSCRIBE = 'GRPC_SUBSCRIBE';

export interface GrpcHandlerMetadata {
    method: string;
    version: string;
    service: string;
}

export function GrpcSubscribe<
    S extends ServiceName,
    M extends ServiceMethod<S>,
    V extends ServiceVersion<S, M>,
>(service: S, method: M, version: V): MethodDecorator {
    return SetMetadata(GRPC_SUBSCRIBE, {
        service: service as string,
        method: method as string,
        version: version as string,
    } satisfies GrpcHandlerMetadata);
}
