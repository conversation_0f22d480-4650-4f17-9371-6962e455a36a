// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/get-user-permission/messages.proto

/* eslint-disable */
import { type V1MonolithGetUserPermissionRequest, type V1MonolithGetUserPermissionResponse } from "./versions/v1";

export const protobufPackage = "monolith";

/** Универсальное описание запроса с выбором версии */
export interface MonolithGetUserPermissionRequest {
  v1?: V1MonolithGetUserPermissionRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface MonolithGetUserPermissionResponse {
  v1?: V1MonolithGetUserPermissionResponse | undefined;
}
