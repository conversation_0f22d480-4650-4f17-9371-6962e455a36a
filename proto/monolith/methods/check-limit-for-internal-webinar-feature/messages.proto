syntax = "proto3";

package monolith;

// Импортируем определения конкретных версий
import "monolith/methods/check-limit-for-internal-webinar-feature/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message MonolithCheckLimitForInternalWebinarFeatureRequest {
  oneof version {
    V1MonolithCheckLimitForInternalWebinarFeatureRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message MonolithCheckLimitForInternalWebinarFeatureResponse {
  oneof version {
    V1MonolithCheckLimitForInternalWebinarFeatureResponse v1 = 1;
  }
}
