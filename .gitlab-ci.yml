stages:
  - test      # Запуск линтеров, тестов и проверки сборки
  - build     # Сборка Docker-образа
  - release   # Создание релиза и публикация NPM пакета

variables:
  SEMANTIC_RELEASE_VERSION: 24.2.3
  NODE_VERSION: 20
  ALPINE_VERSION: 3.21
  # Настройки PNPM
  PNPM_VERSION: 9.15.4

# Шаблоны
.node-setup:
  image: node:$NODE_VERSION-alpine$ALPINE_VERSION
  before_script:
    - echo "https://mirror.yandex.ru/mirrors/alpine/v$ALPINE_VERSION/main" > /etc/apk/repositories
    - echo "https://mirror.yandex.ru/mirrors/alpine/v$ALPINE_VERSION/community" >> /etc/apk/repositories
    - apk update
    - apk add --no-cache git curl
    - corepack enable
    - corepack prepare pnpm@$PNPM_VERSION --activate
  tags:
    - docker

.node-npmrc:
  extends: .node-setup
  before_script:
    - echo "https://mirror.yandex.ru/mirrors/alpine/v$ALPINE_VERSION/main" > /etc/apk/repositories
    - echo "https://mirror.yandex.ru/mirrors/alpine/v$ALPINE_VERSION/community" >> /etc/apk/repositories
    - apk update
    - apk add --no-cache git curl
    - corepack enable
    - corepack prepare pnpm@$PNPM_VERSION --activate
    - |
      cat > .npmrc << EOL
      @skillspace:registry=https://gitlab.sksp.site/api/v4/packages/npm/
      //gitlab.sksp.site/:_authToken=${NPM_TOKEN_SKILLSPACE}
      always-auth=true
      EOL
    - pnpm install --no-frozen-lockfile

build:
  image: node:$NODE_VERSION-alpine3.21
  stage: build
  tags:
    - docker
  script:
    - apk add --no-cache protobuf
    - corepack enable && corepack prepare pnpm@9.15.4 --activate
    - pnpm install --no-frozen-lockfile
    - pnpm run build
  only:
    - merge_requests

release:
  image: node:$NODE_VERSION-alpine3.21
  stage: release
  tags:
    - docker
  before_script:
    - apk add --no-cache protobuf
    - echo "https://mirror.yandex.ru/mirrors/alpine/v$ALPINE_VERSION/main" > /etc/apk/repositories
    - echo "https://mirror.yandex.ru/mirrors/alpine/v$ALPINE_VERSION/community" >> /etc/apk/repositories
    - apk update
    - apk add --no-cache jq curl git openssl python3 make g++ openssh-client
    - corepack enable && corepack prepare pnpm@9.15.4 --activate

    # Настройка SSH для deploy keys с исправлением проблемы
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$DEPLOY_CI_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan gitlab.sksp.site >> ~/.ssh/known_hosts

    - git config user.name "GitLab CI"
    - git config user.email "********************"
    - pnpm install --no-frozen-lockfile
    - pnpm add -D semantic-release@$SEMANTIC_RELEASE_VERSION @semantic-release/gitlab @semantic-release/git @semantic-release/changelog
  script:
    # Настройка Git для работы с GitLab через SSH вместо HTTPS+токен
    - git remote set-<NAME_EMAIL>:${CI_PROJECT_PATH}.git

    # Настройка среды для semantic-release
    - export GL_TOKEN=${NPM_TOKEN_SKILLSPACE}
    - export GL_URL=https://gitlab.sksp.site
    - export NPM_TOKEN=${NPM_TOKEN_SKILLSPACE}

    # Настройка NPM для работы с GitLab Registry
    - echo "@${CI_PROJECT_ROOT_NAMESPACE}:registry=https://gitlab.sksp.site/api/v4/projects/$CI_PROJECT_ID/packages/npm/" > .npmrc
    - echo "//gitlab.sksp.site/api/v4/projects/$CI_PROJECT_ID/packages/npm/:_authToken=${NPM_TOKEN_SKILLSPACE}" >> .npmrc
    - echo "//gitlab.sksp.site/api/v4/packages/npm/:_authToken=${NPM_TOKEN_SKILLSPACE}" >> .npmrc

    # Настройка Git для работы с GitLab через SSH вместо HTTPS+токен
    - git remote set-<NAME_EMAIL>:${CI_PROJECT_PATH}.git

    # Отключаем husky для CI
    - npm pkg set scripts.test="exit 0"
    - npm pkg delete scripts.prepare
    - rm -rf .husky

    # Проверяем/создаём CHANGELOG.md
    - |
      if [ ! -f CHANGELOG.md ]; then
        touch CHANGELOG.md
        git add CHANGELOG.md
        git commit -m "chore: add initial CHANGELOG.md [skip ci]" || true
        # пушим в ту же ветку; [skip ci] не запустит новый pipeline
        git push origin HEAD:$CI_COMMIT_REF_NAME
      fi

    # Запуск сборки
    - pnpm run build

    # Запуск semantic-release
    - npx semantic-release
  artifacts:
    paths:
      - dist/
      - package.json
      - CHANGELOG.md
    expire_in: 1 week
  only:
    - main
