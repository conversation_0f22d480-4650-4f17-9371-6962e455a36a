syntax = "proto3";

package monolith;

// Импортируем определения конкретных версий
import "monolith/methods/get-kinescope-by-school/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message MonolithGetKinescopeBySchoolRequest {
  oneof version {
    V1MonolithGetKinescopeBySchoolRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message MonolithGetKinescopeBySchoolResponse {
  oneof version {
    V1MonolithGetKinescopeBySchoolResponse v1 = 1;
  }
}
