syntax = "proto3";

package monolith;

// Импортируем определения конкретных версий
import "monolith/methods/check-validity-union-auth-key/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message MonolithCheckValidityUnionAuthKeyRequest {
  oneof version {
    V1MonolithCheckValidityUnionAuthKeyRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message MonolithCheckValidityUnionAuthKeyResponse {
  oneof version {
    V1MonolithCheckValidityUnionAuthKeyResponse v1 = 1;
  }
}
