import * as fs from 'fs';
import * as path from 'path';

const BASE_PROTO_DIR = path.join(__dirname, '../proto');
const GENERATED_DIR = path.join(__dirname, '../src/generated');
const OUTPUT_PATH_FILE = path.join(GENERATED_DIR, 'proto-paths.ts');

if (!fs.existsSync(GENERATED_DIR)) {
    fs.mkdirSync(GENERATED_DIR, { recursive: true });
}

function findServiceProtoFiles(dir: string): string[] {
    return fs.readdirSync(dir, { withFileTypes: true }).flatMap((entry) => {
        const fullPath = path.join(dir, entry.name);
        return entry.isDirectory()
            ? findServiceProtoFiles(fullPath)
            : entry.isFile() && entry.name === 'service.proto'
              ? [fullPath]
              : [];
    });
}

const allFiles = findServiceProtoFiles(BASE_PROTO_DIR);

const protoExports = allFiles.map((file) => {
    const folder = path
        .basename(path.dirname(file))
        .replace(/[^a-zA-Z0-9]/g, '_');
    const name = `${folder}ServiceProtoPath`;
    const relativePath = path
        .relative(path.join(__dirname, '../src'), file)
        .replace(/\\/g, '/');
    return `export const ${name} = path.resolve(__dirname, './${relativePath}');`;
});

const protoContent = `// GENERATED FILE — DO NOT EDIT
import * as path from 'path';

${protoExports.join('\n')}
`;

fs.writeFileSync(OUTPUT_PATH_FILE, protoContent);
console.log('✅ proto-paths.ts сгенерирован');
