"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const BASE_PROTO_DIR = path.join(__dirname, '../proto');
const GENERATED_DIR = path.join(__dirname, '../src/generated');
const OUTPUT_PATH_FILE = path.join(GENERATED_DIR, 'proto-paths.ts');
if (!fs.existsSync(GENERATED_DIR)) {
    fs.mkdirSync(GENERATED_DIR, { recursive: true });
}
function findServiceProtoFiles(dir) {
    return fs.readdirSync(dir, { withFileTypes: true }).flatMap((entry) => {
        const fullPath = path.join(dir, entry.name);
        return entry.isDirectory()
            ? findServiceProtoFiles(fullPath)
            : entry.isFile() && entry.name === 'service.proto'
                ? [fullPath]
                : [];
    });
}
const allFiles = findServiceProtoFiles(BASE_PROTO_DIR);
const protoExports = allFiles.map((file) => {
    const folder = path
        .basename(path.dirname(file))
        .replace(/[^a-zA-Z0-9]/g, '_');
    const name = `${folder}ServiceProtoPath`;
    const relativePath = path
        .relative(path.join(__dirname, '../src'), file)
        .replace(/\\/g, '/');
    return `export const ${name} = path.resolve(__dirname, './${relativePath}');`;
});
const protoContent = `// GENERATED FILE — DO NOT EDIT
import * as path from 'path';

${protoExports.join('\n')}
`;
fs.writeFileSync(OUTPUT_PATH_FILE, protoContent);
console.log('✅ proto-paths.ts сгенерирован');
