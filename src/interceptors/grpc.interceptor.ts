import {
    CallHandler,
    ExecutionContext,
    Injectable,
    Logger,
    NestInterceptor,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';

@Injectable()
export class GrpcInterceptor implements NestInterceptor {
    private readonly logger = new Logger(GrpcInterceptor.name);

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        // Проверка, что это gRPC-контекст
        if (context.getType() !== 'rpc') {
            return next.handle();
        }

        const handler = context.getHandler();
        const methodName = handler?.name || 'unknown';

        const rpcContext = context.switchToRpc();
        const data = rpcContext.getData();
        const metadata = rpcContext.getContext()?.metadata?.getMap?.() ?? {};

        const requestId = metadata['x-request-id'] ?? 'unknown';

        this.logger.debug(
            `📥 gRPC вызов: ${methodName} [requestId=${requestId}]`,
        );

        this.logger.debug(
            { data, metadata },
            `➡️ Входящие данные для метода: ${methodName}`,
        );

        return next.handle().pipe(
            tap((response) => {
                this.logger.debug(
                    { response },
                    `📤 Ответ от gRPC метода: ${methodName} [requestId=${requestId}]`,
                );
            }),
        );
    }
}
