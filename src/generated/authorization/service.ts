// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: authorization/service.proto

/* eslint-disable */
import {
  type AuthorizationGetUserPermissionRequest,
  type AuthorizationGetUserPermissionResponse,
} from "./methods/get-user-permission/messages";

export const protobufPackage = "authorization";

export interface AuthorizationService {
  /** Получение информации о доступах пользователя в школе */
  GetUserPermission(request: AuthorizationGetUserPermissionRequest): Promise<AuthorizationGetUserPermissionResponse>;
}
