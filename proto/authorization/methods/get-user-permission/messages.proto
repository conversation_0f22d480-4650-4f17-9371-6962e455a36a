syntax = "proto3";

package authorization;

// Импортируем определения конкретных версий
import "authorization/methods/get-user-permission/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message AuthorizationGetUserPermissionRequest {
  oneof version {
    V1AuthorizationGetUserPermissionRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message AuthorizationGetUserPermissionResponse {
  oneof version {
    V1AuthorizationGetUserPermissionResponse v1 = 1;
  }
}
