"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_fs_1 = __importDefault(require("node:fs"));
const node_path_1 = __importDefault(require("node:path"));
const PROTO_DIR = node_path_1.default.resolve(__dirname, '../proto');
const GENERATED_DIR = node_path_1.default.resolve(__dirname, '../src/generated');
const OUTPUT_PATH = node_path_1.default.resolve(GENERATED_DIR, 'grpc-types.d.ts');
function findAllServiceProtos() {
    const result = [];
    function walk(dir) {
        const entries = node_fs_1.default.readdirSync(dir, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = node_path_1.default.join(dir, entry.name);
            if (entry.isDirectory()) {
                walk(fullPath);
            }
            else if (entry.name === 'service.proto') {
                result.push(fullPath);
            }
        }
    }
    walk(PROTO_DIR);
    return result;
}
function getProtoPackageAndService(content) {
    const packageMatch = content.match(/^\s*package (.*?);/m);
    const serviceMatch = content.match(/^\s*service (.*?)( |\{)/m);
    if (!packageMatch || !serviceMatch)
        return null;
    return {
        packageName: packageMatch[1],
        serviceName: serviceMatch[1],
    };
}
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
function getMethods(dir) {
    const methodsPath = node_path_1.default.resolve(dir, 'methods');
    if (!node_fs_1.default.existsSync(methodsPath))
        return [];
    return node_fs_1.default.readdirSync(methodsPath).filter((name) => {
        const fullPath = node_path_1.default.join(methodsPath, name);
        return node_fs_1.default.statSync(fullPath).isDirectory();
    });
}
function getVersions(messagesTsPath) {
    if (!node_fs_1.default.existsSync(messagesTsPath))
        return [];
    const file = node_fs_1.default.readFileSync(messagesTsPath, 'utf-8');
    const matches = [...file.matchAll(/\b(v[0-9]+)\?:/g)];
    return matches.map((m) => m[1]);
}
const serviceMethods = {};
const imports = new Set();
for (const protoPath of findAllServiceProtos()) {
    const protoContent = node_fs_1.default.readFileSync(protoPath, 'utf-8');
    const parsed = getProtoPackageAndService(protoContent);
    if (!parsed)
        continue;
    const { packageName, serviceName } = parsed;
    const serviceKey = serviceName;
    const protoRelativeDir = node_path_1.default.relative(PROTO_DIR, node_path_1.default.dirname(protoPath));
    const generatedBasePath = node_path_1.default.resolve(GENERATED_DIR, protoRelativeDir);
    const methods = getMethods(node_path_1.default.resolve(PROTO_DIR, protoRelativeDir));
    const serviceEntry = {};
    for (const method of methods) {
        const methodName = method
            .split('-')
            .map((p) => capitalize(p))
            .join('');
        const importIdentifierReq = `${methodName}Request`;
        const importIdentifierRes = `${methodName}Response`;
        imports.add(`type { ${importIdentifierReq}, ${importIdentifierRes} } from './${protoRelativeDir}/methods/${method}/messages';`);
        const messagesTsPath = node_path_1.default.resolve(generatedBasePath, 'methods', method, 'messages.ts');
        const versions = getVersions(messagesTsPath);
        const versionMap = {};
        for (const version of versions) {
            versionMap[version] = {
                request: `${importIdentifierReq}['${version}']`,
                response: `${importIdentifierRes}['${version}']`,
            };
        }
        serviceEntry[methodName] = versionMap;
    }
    serviceMethods[serviceKey] = serviceEntry;
}
const lines = [];
lines.push('// GENERATED FILE — DO NOT EDIT\n');
for (const imp of imports) {
    lines.push(`import ${imp}`);
}
lines.push('\nexport type ServiceMethods = ' +
    JSON.stringify(serviceMethods, null, 4)
        .replace(/"(\w+)":/g, '$1:')
        .replace(/"/g, '') +
    ';\n');
lines.push('export type ServiceName = keyof ServiceMethods;');
lines.push('export type ServiceMethod<T extends ServiceName> = keyof ServiceMethods[T];');
lines.push('export type ServiceVersion<T extends ServiceName, M extends ServiceMethod<T>> = keyof ServiceMethods[T][M];');
lines.push('');
lines.push('export type ServiceRequest<T extends ServiceName, M extends ServiceMethod<T>, V extends ServiceVersion<T, M>> =');
lines.push("    ServiceMethods[T][M][V]['request'];");
lines.push('');
lines.push('export type ServiceResponse<T extends ServiceName, M extends ServiceMethod<T>, V extends ServiceVersion<T, M>> =');
lines.push("    ServiceMethods[T][M][V]['response'];");
node_fs_1.default.writeFileSync(OUTPUT_PATH, lines.join('\n'));
console.log(`✅ grpc-types.d.ts сгенерирован: ${OUTPUT_PATH}`);
