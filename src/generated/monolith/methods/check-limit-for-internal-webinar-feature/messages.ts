// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/check-limit-for-internal-webinar-feature/messages.proto

/* eslint-disable */
import {
  type V1MonolithCheckLimitForInternalWebinarFeatureRequest,
  type V1MonolithCheckLimitForInternalWebinarFeatureResponse,
} from "./versions/v1";

export const protobufPackage = "monolith";

/** Универсальное описание запроса с выбором версии */
export interface MonolithCheckLimitForInternalWebinarFeatureRequest {
  v1?: V1MonolithCheckLimitForInternalWebinarFeatureRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface MonolithCheckLimitForInternalWebinarFeatureResponse {
  v1?: V1MonolithCheckLimitForInternalWebinarFeatureResponse | undefined;
}
