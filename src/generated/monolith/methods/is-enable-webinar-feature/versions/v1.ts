// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/is-enable-webinar-feature/versions/v1.proto

/* eslint-disable */

export const protobufPackage = "monolith";

export interface V1MonolithIsEnableWebinarFeatureRequest {
  uuid: string;
}

export interface V1MonolithIsEnableWebinarFeatureResponse {
  enabled: boolean;
  error: string;
  limit: number;
}
