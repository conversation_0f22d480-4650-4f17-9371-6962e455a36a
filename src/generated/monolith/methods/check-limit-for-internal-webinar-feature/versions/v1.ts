// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/check-limit-for-internal-webinar-feature/versions/v1.proto

/* eslint-disable */

export const protobufPackage = "monolith";

export interface V1MonolithCheckLimitForInternalWebinarFeatureRequest {
  uuid: string;
}

export interface V1MonolithCheckLimitForInternalWebinarFeatureResponse {
  enabled: boolean;
  error: string;
  limit: number;
}
