syntax = "proto3";

package monolith;

import "monolith/methods/get-kinescope-by-school/messages.proto";
import "monolith/methods/check-limit-for-internal-webinar-feature/messages.proto";
import "monolith/methods/is-enable-webinar-feature/messages.proto";
import "monolith/methods/get-user-permission/messages.proto";
import "monolith/methods/check-validity-union-auth-key/messages.proto";

service MonolithService {
  // Получение информации об идентификаторе проекта школы в kinescope по uuid школы
  rpc GetKinescopeBySchool(MonolithGetKinescopeBySchoolRequest) returns (MonolithGetKinescopeBySchoolResponse);

  // Получение информации о доступности фичи встроенных вебинаров для школы
  rpc IsEnableWebinarFeature(MonolithIsEnableWebinarFeatureRequest) returns (MonolithIsEnableWebinarFeatureResponse);

  // Проверка количества доступных встроенных вебинаров для школы в месяц
  rpc CheckLimitForInternalWebinarFeature(MonolithCheckLimitForInternalWebinarFeatureRequest) returns (MonolithCheckLimitForInternalWebinarFeatureResponse);

  // Получение информации о доступах пользователя в школе
  rpc GetUserPermission(MonolithGetUserPermissionRequest) returns (MonolithGetUserPermissionResponse) {};

  // Валидация union auth key пользователя и подключения ТГ
  rpc CheckValidityUnionAuthKey(MonolithCheckValidityUnionAuthKeyRequest) returns (MonolithCheckValidityUnionAuthKeyResponse) {};
}
