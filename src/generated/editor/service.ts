// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: editor/service.proto

/* eslint-disable */
import {
  type EditorCreateManyPagesRequest,
  type EditorCreateManyPagesResponse,
} from "./methods/create-many-pages/messages";
import {
  type EditorRemoveManyPagesRequest,
  type EditorRemoveManyPagesResponse,
} from "./methods/remove-many-pages/messages";

export const protobufPackage = "editor";

export interface EditorService {
  /** Создание страниц */
  CreateManyPages(request: EditorCreateManyPagesRequest): Promise<EditorCreateManyPagesResponse>;
  /** Удаление страниц */
  RemoveManyPages(request: EditorRemoveManyPagesRequest): Promise<EditorRemoveManyPagesResponse>;
}
