// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: plans/methods/is-feature-available/messages.proto

/* eslint-disable */
import { type V1PlansIsFeatureAvailableRequest, type V1PlansIsFeatureAvailableResponse } from "./versions/v1";

export const protobufPackage = "plans";

/** Универсальное описание запроса с выбором версии */
export interface PlansIsFeatureAvailableRequest {
  v1?: V1PlansIsFeatureAvailableRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface PlansIsFeatureAvailableResponse {
  v1?: V1PlansIsFeatureAvailableResponse | undefined;
}
