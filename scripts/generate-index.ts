import * as fs from 'fs';
import * as path from 'path';

const GENERATED_DIR = path.join(__dirname, '../src/generated');
const OUTPUT_FILE = path.join(GENERATED_DIR, 'index.ts');

if (!fs.existsSync(GENERATED_DIR)) {
    fs.mkdirSync(GENERATED_DIR, { recursive: true });
}

const exportsLines = fs
    .readdirSync(GENERATED_DIR)
    .filter((file) => file !== 'index.ts' && file.endsWith('.ts'))
    .map((file) => `export * from './${file.replace(/\.ts$/, '')}';`);

const content = `// GENERATED FILE — DO NOT EDIT

${exportsLines.join('\n')}
`;

fs.writeFileSync(OUTPUT_FILE, content);
console.log('✅ index.ts в generated обновлён');
