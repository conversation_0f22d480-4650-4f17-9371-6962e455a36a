// GENERATED FILE — DO NOT EDIT

import type { AuthorizationGetUserPermissionRequest, AuthorizationGetUserPermissionResponse } from './authorization/methods/get-user-permission/messages';
import type { EditorCreateManyPagesRequest, EditorCreateManyPagesResponse } from './editor/methods/create-many-pages/messages';
import type { EditorRemoveManyPagesRequest, EditorRemoveManyPagesResponse } from './editor/methods/remove-many-pages/messages';
import type { MonolithCheckLimitForInternalWebinarFeatureRequest, MonolithCheckLimitForInternalWebinarFeatureResponse } from './monolith/methods/check-limit-for-internal-webinar-feature/messages';
import type { MonolithCheckValidityUnionAuthKeyRequest, MonolithCheckValidityUnionAuthKeyResponse } from './monolith/methods/check-validity-union-auth-key/messages';
import type { MonolithGetKinescopeBySchoolRequest, MonolithGetKinescopeBySchoolResponse } from './monolith/methods/get-kinescope-by-school/messages';
import type { MonolithGetUserPermissionRequest, MonolithGetUserPermissionResponse } from './monolith/methods/get-user-permission/messages';
import type { MonolithIsEnableWebinarFeatureRequest, MonolithIsEnableWebinarFeatureResponse } from './monolith/methods/is-enable-webinar-feature/messages';
import type { PlansIsFeatureAvailableRequest, PlansIsFeatureAvailableResponse } from './plans/methods/is-feature-available/messages';

export type ServiceMethods = {
    AuthorizationService: {
        GetUserPermission: {
            v1: {
                request: AuthorizationGetUserPermissionRequest['v1'],
                response: AuthorizationGetUserPermissionResponse['v1']
            }
        }
    },
    EditorService: {
        CreateManyPages: {
            v1: {
                request: EditorCreateManyPagesRequest['v1'],
                response: EditorCreateManyPagesResponse['v1']
            }
        },
        RemoveManyPages: {
            v1: {
                request: EditorRemoveManyPagesRequest['v1'],
                response: EditorRemoveManyPagesResponse['v1']
            }
        }
    },
    MonolithService: {
        CheckLimitForInternalWebinarFeature: {
            v1: {
                request: MonolithCheckLimitForInternalWebinarFeatureRequest['v1'],
                response: MonolithCheckLimitForInternalWebinarFeatureResponse['v1']
            }
        },
        CheckValidityUnionAuthKey: {
            v1: {
                request: MonolithCheckValidityUnionAuthKeyRequest['v1'],
                response: MonolithCheckValidityUnionAuthKeyResponse['v1']
            }
        },
        GetKinescopeBySchool: {
            v1: {
                request: MonolithGetKinescopeBySchoolRequest['v1'],
                response: MonolithGetKinescopeBySchoolResponse['v1']
            }
        },
        GetUserPermission: {
            v1: {
                request: MonolithGetUserPermissionRequest['v1'],
                response: MonolithGetUserPermissionResponse['v1']
            }
        },
        IsEnableWebinarFeature: {
            v1: {
                request: MonolithIsEnableWebinarFeatureRequest['v1'],
                response: MonolithIsEnableWebinarFeatureResponse['v1']
            }
        }
    },
    PlansService: {
        IsFeatureAvailable: {
            v1: {
                request: PlansIsFeatureAvailableRequest['v1'],
                response: PlansIsFeatureAvailableResponse['v1']
            }
        }
    }
};

export type ServiceName = keyof ServiceMethods;
export type ServiceMethod<T extends ServiceName> = keyof ServiceMethods[T];
export type ServiceVersion<T extends ServiceName, M extends ServiceMethod<T>> = keyof ServiceMethods[T][M];

type VersionEntry = {
    request: any;
    response: any;
};

export type ServiceRequest<
    T extends ServiceName,
    M extends ServiceMethod<T>,
    V extends ServiceVersion<T, M>
> = ServiceMethods[T][M][V] extends VersionEntry
    ? ServiceMethods[T][M][V]['request']
    : never;

export type ServiceResponse<
    T extends ServiceName,
    M extends ServiceMethod<T>,
    V extends ServiceVersion<T, M>
> = ServiceMethods[T][M][V] extends VersionEntry
    ? ServiceMethods[T][M][V]['response']
    : never;

export type ServiceDirType = 'authorization' | 'editor' | 'monolith' | 'plans';

export const ServiceDirMap: Record<ServiceName, ServiceDirType> = {
    AuthorizationService: 'authorization',
    EditorService: 'editor',
    MonolithService: 'monolith',
    PlansService: 'plans',
};
