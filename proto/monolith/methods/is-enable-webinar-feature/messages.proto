syntax = "proto3";

package monolith;

// Импортируем определения конкретных версий
import "monolith/methods/is-enable-webinar-feature/versions/v1.proto";

// Универсальное описание запроса с выбором версии
message MonolithIsEnableWebinarFeatureRequest {
  oneof version {
    V1MonolithIsEnableWebinarFeatureRequest v1 = 1;
  }
}

// Универсальное описание ответа с выбором версии
message MonolithIsEnableWebinarFeatureResponse {
  oneof version {
    V1MonolithIsEnableWebinarFeatureResponse v1 = 1;
  }
}
