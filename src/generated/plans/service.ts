// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: plans/service.proto

/* eslint-disable */
import {
  type PlansIsFeatureAvailableRequest,
  type PlansIsFeatureAvailableResponse,
} from "./methods/is-feature-available/messages";

export const protobufPackage = "plans";

export interface PlansService {
  /** Получение информации о доступности фичи */
  IsFeatureAvailable(request: PlansIsFeatureAvailableRequest): Promise<PlansIsFeatureAvailableResponse>;
}
