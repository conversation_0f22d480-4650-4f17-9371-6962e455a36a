syntax = "proto3";

package monolith;

message UserPermissionUser
{
  string name = 1;
  string email = 2;
  string unionAuthKey = 3;
}

message V1MonolithGetUserPermissionRequest
{
  string schoolId = 1;
  string userId = 2;
}

message V1MonolithGetUserPermissionResponse
{
  bool verify = 1;
  string error = 2;
  string role = 3;
  string schoolId = 4;
  UserPermissionUser user = 5;
  repeated string actions = 6;

}
