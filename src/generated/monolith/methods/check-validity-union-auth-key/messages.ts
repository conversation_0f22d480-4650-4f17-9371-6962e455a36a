// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/check-validity-union-auth-key/messages.proto

/* eslint-disable */
import {
  type V1MonolithCheckValidityUnionAuthKeyRequest,
  type V1MonolithCheckValidityUnionAuthKeyResponse,
} from "./versions/v1";

export const protobufPackage = "monolith";

/** Универсальное описание запроса с выбором версии */
export interface MonolithCheckValidityUnionAuthKeyRequest {
  v1?: V1MonolithCheckValidityUnionAuthKeyRequest | undefined;
}

/** Универсальное описание ответа с выбором версии */
export interface MonolithCheckValidityUnionAuthKeyResponse {
  v1?: V1MonolithCheckValidityUnionAuthKeyResponse | undefined;
}
