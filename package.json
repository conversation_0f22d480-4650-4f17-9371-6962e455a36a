{"name": "@skillspace/grpc", "version": "1.7.0", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "commonjs", "description": "GRPC контракты", "publishConfig": {"registry": "https://gitlab.sksp.site/api/v4/packages/npm/"}, "scripts": {"start-test-app": "ts-node src/main.ts", "generate:types": "ts-node scripts/generate-service-methods.ts", "generate:paths": "ts-node scripts/generate-proto-paths.ts", "generate:index": "ts-node scripts/generate-index.ts", "generate:proto": "mkdir -p src/generated && find proto -name '*.proto' | xargs protoc --plugin=protoc-gen-ts=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_out=src/generated --proto_path=proto --ts_proto_opt=outputClientImpl=false,env=none,outputEncodeMethods=false,outputJsonMethods=false", "copy:assets": "ts-node scripts/copy-assets.ts", "generate": "pnpm run generate:proto && pnpm run generate:types && pnpm run generate:paths && pnpm run generate:index", "build": "pnpm run generate && tsc -p tsconfig.json && pnpm run copy:assets", "dev": "nodemon --watch proto --watch src/generated --ext ts,proto --exec \"npm run generate\"", "lint": "eslint ../contracts/grpc-contracts", "lint:fix": "eslint ../contracts/grpc-contracts --fix", "format": "prettier --write ../contracts/grpc-contracts", "link": "pnpm build && pnpm link --global", "release": "npx semantic-release", "release-skip-range": "npx semantic-release --no-verify-release-range", "test": "exit 0"}, "dependencies": {"@grpc/grpc-js": "^1.9.4", "@grpc/proto-loader": "^0.7.13", "protobufjs": "^7.2.4", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.23.0", "@nestjs/platform-express": "^11.0.12", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.4", "@skillspace/hermes": "^1.0.1", "@types/fs-extra": "^11.0.4", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "conventional-changelog-cli": "2.2.2", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.2.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "fs-extra": "^11.3.0", "globals": "^16.0.0", "husky": "^8.0.0", "jq": "^1.7.2", "nodemon": "^3.1.9", "prettier": "^3.5.3", "semantic-release": "24.2.3", "semver": "7.5.4", "ts-node": "^10.9.2", "ts-proto": "2.7.0", "typescript": "^5.2.2"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "reflect-metadata": "^0.1.14"}, "files": ["dist", "proto", "src/generated", "src/proto-paths.ts"], "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}