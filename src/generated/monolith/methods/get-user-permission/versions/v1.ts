// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.12.4
// source: monolith/methods/get-user-permission/versions/v1.proto

/* eslint-disable */

export const protobufPackage = "monolith";

export interface UserPermissionUser {
  name: string;
  email: string;
  unionAuthKey: string;
}

export interface V1MonolithGetUserPermissionRequest {
  schoolId: string;
  userId: string;
}

export interface V1MonolithGetUserPermissionResponse {
  verify: boolean;
  error: string;
  role: string;
  schoolId: string;
  user: UserPermissionUser | undefined;
  actions: string[];
}
